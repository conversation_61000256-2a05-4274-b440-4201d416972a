<template>
	<div
		class="modern-control-panel"
		:class="{ collapsed: isCollapsed }"
	>
		<div class="panel-container">
			<!-- 收缩状态下的垂直导航 -->
			<div
				class="collapsed-nav"
				v-if="isCollapsed"
			>
				<div
					class="collapsed-nav-item"
					title="视角控制"
					@click="$emit('camera-reset')"
				>
					<el-icon><View /></el-icon>
				</div>
				<div
					class="collapsed-nav-item"
					title="灯光控制"
				>
					<el-icon><Sunny /></el-icon>
				</div>
			</div>

			<!-- 视角控制 -->
			<div
				class="panel-section"
				v-if="!isCollapsed"
			>
				<div class="section-header">
					<div class="header-content">
						<el-icon class="header-icon"><View /></el-icon>
						<span class="header-title">视角控制</span>
					</div>
				</div>
				<div class="section-content">
					<!-- 视角控制按钮组 -->
					<div class="control-buttons">
						<el-button
							type="primary"
							@click="$emit('camera-reset')"
							class="action-button reset-camera-btn"
						>
							<el-icon class="btn-icon"><Refresh /></el-icon>
							<span class="btn-text">重置视角</span>
							<span class="shortcut-hint">R</span>
						</el-button>
					</div>
				</div>
			</div>

			<!-- 材质控制 -->
			<div
				class="panel-section"
				v-if="!isCollapsed"
			>
				<div class="section-header">
					<div class="header-content">
						<el-icon class="header-icon"><Monitor /></el-icon>
						<span class="header-title">材质控制</span>
					</div>
				</div>
				<div class="section-content">
					<!-- 材质透明度控制 -->
					<div class="slider-container">
						<div class="slider-header">
							<span class="slider-label">墙窗透明度</span>
							<span class="slider-value"
								>{{ Math.round(materialTransparencyValue * 100) }}%</span
							>
						</div>
						<el-slider
							v-model="materialTransparencyValue"
							:min="0"
							:max="1"
							:step="0.05"
							:show-tooltip="false"
							@change="handleMaterialTransparencyChange"
						/>
					</div>
				</div>
			</div>

			<!-- 灯光控制 -->
			<div
				class="panel-section"
				v-if="!isCollapsed"
			>
				<div class="section-header">
					<div class="header-content">
						<el-icon class="header-icon"><Sunny /></el-icon>
						<span class="header-title">灯光控制</span>
					</div>
				</div>
				<div class="section-content">
					<!-- 环境光强度控制 -->
					<div class="slider-container">
						<div class="slider-header">
							<span class="slider-label">环境光强度</span>
							<span class="slider-value">{{
								ambientLightIntensity.toFixed(1)
							}}</span>
						</div>
						<el-slider
							v-model="ambientLightIntensity"
							:min="0"
							:max="1"
							:step="0.1"
							:show-tooltip="false"
							@change="handleAmbientLightChange"
						/>
					</div>

					<!-- 环境光颜色控制 -->
					<div class="color-container">
						<div class="color-header">
							<span class="color-label">环境光颜色</span>
						</div>
						<el-color-picker
							v-model="ambientLightColor"
							@change="handleAmbientLightColorChange"
							show-alpha
							size="small"
						/>
					</div>

					<!-- 平行光强度控制 -->
					<div class="slider-container">
						<div class="slider-header">
							<span class="slider-label">平行光强度</span>
							<span class="slider-value">{{
								directionalLightIntensity.toFixed(1)
							}}</span>
						</div>
						<el-slider
							v-model="directionalLightIntensity"
							:min="0"
							:max="1"
							:step="0.1"
							:show-tooltip="false"
							@change="handleDirectionalLightChange"
						/>
					</div>

					<!-- 平行光颜色控制 -->
					<div class="color-container">
						<div class="color-header">
							<span class="color-label">平行光颜色</span>
						</div>
						<el-color-picker
							v-model="directionalLightColor"
							@change="handleDirectionalLightColorChange"
							show-alpha
							size="small"
						/>
					</div>

					<!-- 平行光位置控制 -->
					<div class="position-container">
						<div class="position-header">
							<span class="position-label">平行光位置</span>
						</div>
						<div class="position-controls">
							<div class="position-row">
								<span class="axis-label">X:</span>
								<el-slider
									v-model="directionalLightPosition.x"
									:min="-500"
									:max="500"
									:step="10"
									:show-tooltip="false"
									@change="handleDirectionalLightPositionChange"
									class="position-slider"
								/>
								<span class="position-value">{{
									directionalLightPosition.x
								}}</span>
							</div>
							<div class="position-row">
								<span class="axis-label">Y:</span>
								<el-slider
									v-model="directionalLightPosition.y"
									:min="0"
									:max="500"
									:step="10"
									:show-tooltip="false"
									@change="handleDirectionalLightPositionChange"
									class="position-slider"
								/>
								<span class="position-value">{{
									directionalLightPosition.y
								}}</span>
							</div>
							<div class="position-row">
								<span class="axis-label">Z:</span>
								<el-slider
									v-model="directionalLightPosition.z"
									:min="-500"
									:max="500"
									:step="10"
									:show-tooltip="false"
									@change="handleDirectionalLightPositionChange"
									class="position-slider"
								/>
								<span class="position-value">{{
									directionalLightPosition.z
								}}</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, watch } from 'vue';
	import { Refresh, View, Monitor, Sunny } from '@element-plus/icons-vue';

	const props = defineProps({
		isCollapsed: {
			type: Boolean,
			default: false,
		},
		viewEngine: {
			type: Object,
			default: null,
		},
		materialTransparency: {
			type: Number,
			default: 0.5,
		},
	});

	const emit = defineEmits([
		'camera-reset',
		'material-transparency-change',
		'ambient-light-change',
		'ambient-light-color-change',
		'directional-light-change',
		'directional-light-color-change',
		'directional-light-position-change',
	]);

	// 材质透明度状态
	const materialTransparencyValue = ref(props.materialTransparency);

	// 灯光控制状态
	const ambientLightIntensity = ref(10.5);
	const ambientLightColor = ref('#ffffff');

	// 平行光控制状态
	const directionalLightIntensity = ref(5.0);
	const directionalLightColor = ref('#ffffff');
	const directionalLightPosition = ref({
		x: -320,
		y: 210,
		z: -210,
	});

	// 处理材质透明度变化
	const handleMaterialTransparencyChange = (value) => {
		console.log('ControlPanel: 材质透明度滑块变化', value);
		emit('material-transparency-change', value);
	};

	// 处理环境光强度变化
	const handleAmbientLightChange = (value) => {
		console.log('ControlPanel: 环境光强度变化', value);
		emit('ambient-light-change', value);
	};

	// 处理环境光颜色变化
	const handleAmbientLightColorChange = (value) => {
		console.log('ControlPanel: 环境光颜色变化', value);
		emit('ambient-light-color-change', value);
	};

	// 处理平行光强度变化
	const handleDirectionalLightChange = (value) => {
		console.log('ControlPanel: 平行光强度变化', value);
		emit('directional-light-change', value);
	};

	// 处理平行光颜色变化
	const handleDirectionalLightColorChange = (value) => {
		console.log('ControlPanel: 平行光颜色变化', value);
		emit('directional-light-color-change', value);
	};

	// 处理平行光位置变化
	const handleDirectionalLightPositionChange = () => {
		console.log('ControlPanel: 平行光位置变化', directionalLightPosition.value);
		emit('directional-light-position-change', directionalLightPosition.value);
	};

	// 监听props变化
	watch(
		() => props.materialTransparency,
		(newValue) => {
			materialTransparencyValue.value = newValue;
		}
	);
</script>

<style scoped>
	/* 主容器 */
	.modern-control-panel {
		height: 100%;
		background: var(--app-surface-1);
		border-left: 1px solid var(--app-border-primary);
		display: flex;
		flex-direction: column;
		transition: all var(--app-duration-normal) var(--app-ease-out);
		overflow: hidden;
	}

	.modern-control-panel.collapsed {
		width: 60px;
	}

	.panel-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}

	/* 收缩状态导航 */
	.collapsed-nav {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: var(--app-space-md) 0;
		gap: var(--app-space-sm);
	}

	.collapsed-nav-item {
		width: 40px;
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: var(--app-radius-medium);
		background: var(--app-surface-2);
		color: var(--app-text-secondary);
		cursor: pointer;
		transition: all var(--app-duration-fast) var(--app-ease-out);
	}

	.collapsed-nav-item:hover {
		background: var(--app-primary);
		color: white;
		transform: translateY(-2px);
		box-shadow: var(--app-shadow-small);
	}

	/* 面板区块 */
	.panel-section {
		border-bottom: 1px solid var(--app-border-primary);
	}

	.section-header {
		padding: var(--app-space-md);
		background: var(--app-surface-2);
		border-bottom: 1px solid var(--app-border-primary);
	}

	.header-content {
		display: flex;
		align-items: center;
		gap: var(--app-space-sm);
	}

	.header-icon {
		color: var(--app-primary);
		font-size: 18px;
	}

	.header-title {
		font-weight: 600;
		color: var(--app-text-primary);
		font-size: var(--app-font-size-md);
	}

	.section-content {
		padding: var(--app-space-md);
	}

	/* 控制按钮 */
	.control-buttons {
		display: flex;
		flex-direction: column;
		gap: var(--app-space-sm);
		align-items: stretch;
	}

	.action-button {
		width: 100%;
		height: 40px;
		font-weight: 600;
		font-size: 14px;
		border-radius: var(--app-radius-medium);
		transition: all var(--app-duration-fast) var(--app-ease-out);
		border: none;
		box-shadow: 0 2px 6px rgba(64, 158, 255, 0.15);
		display: flex;
		align-items: center;
		justify-content: center;
		gap: var(--app-space-sm);
	}

	.reset-camera-btn {
		background: linear-gradient(135deg, #409eff 0%, #5dade2 100%);
		color: white;
		position: relative;
		overflow: hidden;
	}

	.reset-camera-btn::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(
			90deg,
			transparent,
			rgba(255, 255, 255, 0.2),
			transparent
		);
		transition: left 0.5s;
	}

	.reset-camera-btn:hover::before {
		left: 100%;
	}

	.reset-camera-btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
	}

	.reset-camera-btn:active {
		transform: translateY(0);
		box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
	}

	.btn-icon {
		font-size: 14px;
		flex-shrink: 0;
	}

	.btn-text {
		flex: 1;
		text-align: center;
	}

	.shortcut-hint {
		font-size: var(--app-font-size-xs);
		color: rgba(255, 255, 255, 0.8);
		background: rgba(255, 255, 255, 0.15);
		padding: 2px 6px;
		border-radius: var(--app-radius-small);
		font-weight: 600;
		letter-spacing: 0.5px;
		min-width: 18px;
		text-align: center;
		flex-shrink: 0;
		border: 1px solid rgba(255, 255, 255, 0.2);
	}

	/* 滑块容器样式 */
	.slider-container {
		padding: var(--app-space-md) 0;
	}

	.slider-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: var(--app-space-sm);
	}

	.slider-label {
		font-size: var(--app-font-size-sm);
		color: var(--app-text-secondary);
		font-weight: 500;
	}

	.slider-value {
		font-size: var(--app-font-size-xs);
		background: var(--app-primary);
		color: white;
		padding: 2px 8px;
		border-radius: var(--app-radius-small);
		font-weight: 600;
		min-width: 40px;
		text-align: center;
	}

	/* 颜色选择器容器 */
	.color-container {
		padding: var(--app-space-sm) 0;
		margin-top: var(--app-space-sm);
	}

	.color-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: var(--app-space-sm);
	}

	.color-label {
		font-size: var(--app-font-size-sm);
		color: var(--app-text-secondary);
	}

	/* 位置控制容器 */
	.position-container {
		padding: var(--app-space-sm) 0;
		margin-top: var(--app-space-sm);
	}

	.position-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: var(--app-space-sm);
	}

	.position-label {
		font-size: var(--app-font-size-sm);
		color: var(--app-text-secondary);
	}

	.position-controls {
		display: flex;
		flex-direction: column;
		gap: var(--app-space-sm);
	}

	.position-row {
		display: flex;
		align-items: center;
		gap: var(--app-space-sm);
	}

	.axis-label {
		font-size: var(--app-font-size-xs);
		color: var(--app-text-secondary);
		min-width: 15px;
		font-weight: 600;
	}

	.position-slider {
		flex: 1;
	}

	.position-value {
		font-size: var(--app-font-size-xs);
		background: var(--app-surface-3);
		color: var(--app-text-primary);
		padding: 2px 6px;
		border-radius: var(--app-radius-small);
		min-width: 35px;
		text-align: center;
		border: 1px solid var(--app-border-primary);
	}

	/* Element Plus 样式覆盖 */
	:deep(.el-button) {
		font-size: 14px;
		border: none;
	}

	:deep(.el-button--primary) {
		background: linear-gradient(135deg, #409eff 0%, #5dade2 100%);
		border: none;
	}

	/* 滑块样式覆盖 */
	:deep(.el-slider) {
		margin: 0;
	}

	:deep(.el-slider__runway) {
		background-color: var(--app-surface-3);
		border-radius: 4px;
		height: 6px;
	}

	:deep(.el-slider__bar) {
		background: var(--app-primary-gradient);
		border-radius: 4px;
	}

	:deep(.el-slider__button) {
		border: 2px solid var(--app-primary);
		background-color: white;
		width: 16px;
		height: 16px;
	}

	:deep(.el-slider__button:hover) {
		transform: scale(1.1);
	}
</style>
